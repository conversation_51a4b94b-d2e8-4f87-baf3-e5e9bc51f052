import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Toaster } from "@/components/ui/toaster"
import { AuthProvider } from "@/components/auth-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "WebGenie - AI Website Generator",
    template: "%s | WebGenie"
  },
  description: "Generate beautiful, responsive websites with AI in seconds. No coding required - just describe your vision and watch it come to life.",
  keywords: ["AI", "website generator", "web development", "automation", "no-code", "responsive design", "Tailwind CSS"],
  authors: [{ name: "WebGenie Team" }],
  creator: "WebGenie",
  publisher: "WebGenie",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://webgenie.ai",
    title: "WebGenie - AI Website Generator",
    description: "Generate beautiful, responsive websites with AI in seconds",
    siteName: "WebGenie",
  },
  twitter: {
    card: "summary_large_image",
    title: "WebGenie - AI Website Generator",
    description: "Generate beautiful, responsive websites with AI in seconds",
    creator: "@webgenie",
  },
  verification: {
    google: "google-site-verification-code",
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthProvider>
          {children}
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  )
}
