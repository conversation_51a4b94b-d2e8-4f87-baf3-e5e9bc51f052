import { AuthForm } from "@/components/auth/auth-form"
import { Navbar } from "@/components/landing/navbar"

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-blue-100/20 dark:bg-blue-900/20 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] bg-[length:20px_20px]"></div>
      </div>

      {/* Navigation */}
      <Navbar />

      {/* Auth Form */}
      <div className="relative z-10 w-full max-w-md mx-auto pt-24 px-4">
        <AuthForm mode="signin" />
      </div>
    </div>
  )
}
