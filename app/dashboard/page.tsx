"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { motion } from "framer-motion"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { ChatInterface } from "@/components/dashboard/chat-interface"
import { CodeEditor } from "@/components/dashboard/code-editor"
import { WebsitePreview } from "@/components/dashboard/website-preview"
import { useToast } from "@/hooks/use-toast"
import { FullPageLoading } from "@/components/ui/loading"

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface GeneratedWebsite {
  id: string
  htmlCode: string
  title: string
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedWebsite, setGeneratedWebsite] = useState<GeneratedWebsite | null>(null)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your AI web developer. Describe the website you\'d like me to create for you.',
      timestamp: new Date()
    }
  ])
  const [activeTab, setActiveTab] = useState<'preview' | 'code'>('preview')
  const { toast } = useToast()

  if (status === "loading") {
    return <FullPageLoading message="Loading your dashboard..." />
  }

  if (status === "unauthenticated") {
    redirect("/login")
  }

  const handleSendMessage = async (prompt: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: prompt,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, userMessage])
    setIsGenerating(true)

    try {
      const response = await fetch("/api/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to generate website")
      }

      const data = await response.json()
      setGeneratedWebsite(data)

      // Add assistant response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `I've created a ${data.title} for you! You can see the preview on the right and edit the code if needed.`,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, assistantMessage])

      toast({
        title: "Website generated!",
        description: "Your website has been created successfully.",
      })
    } catch (error) {
      console.error("Generation error:", error)

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Sorry, I encountered an error while generating your website: ${error instanceof Error ? error.message : "Something went wrong. Please try again."}`,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])

      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCodeChange = (newCode: string) => {
    if (generatedWebsite) {
      setGeneratedWebsite({
        ...generatedWebsite,
        htmlCode: newCode
      })
    }
  }

  const handleDownload = () => {
    if (!generatedWebsite) return

    const blob = new Blob([generatedWebsite.htmlCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${generatedWebsite.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Website downloaded!",
      description: "Your HTML file has been saved to your downloads folder.",
    })
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      <DashboardHeader />

      <div className="flex-1 flex overflow-hidden">
        {/* Left Side - Chat Interface */}
        <div className="w-1/3 border-r border-gray-700 flex flex-col">
          <div className="p-4 border-b border-gray-700 bg-gray-800">
            <h2 className="text-lg font-semibold text-white">
              AI Assistant
            </h2>
            <p className="text-sm text-gray-400">
              Describe your website and I'll build it for you
            </p>
          </div>

          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isGenerating}
          />
        </div>

        {/* Right Side - Code Editor and Preview */}
        <div className="flex-1 flex flex-col">
          {generatedWebsite ? (
            <>
              {/* Tab Navigation */}
              <div className="flex border-b border-gray-700 bg-gray-800">
                <button
                  onClick={() => setActiveTab('preview')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'preview'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  Preview
                </button>
                <button
                  onClick={() => setActiveTab('code')}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'code'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  Code
                </button>
                <div className="ml-auto p-2">
                  <button
                    onClick={handleDownload}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    Download HTML
                  </button>
                </div>
              </div>

              {/* Content Area */}
              <div className="flex-1 overflow-hidden">
                {activeTab === 'preview' ? (
                  <WebsitePreview
                    htmlCode={generatedWebsite.htmlCode}
                    title={generatedWebsite.title}
                  />
                ) : (
                  <CodeEditor
                    code={generatedWebsite.htmlCode}
                    onChange={handleCodeChange}
                  />
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <div className="text-6xl mb-4">🚀</div>
                <h3 className="text-xl font-semibold mb-2">Ready to Build?</h3>
                <p className="text-sm">
                  Start a conversation with the AI assistant to create your website
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
