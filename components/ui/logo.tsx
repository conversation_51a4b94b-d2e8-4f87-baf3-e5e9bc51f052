"use client"

import Link from "next/link"
import { Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

interface LogoProps {
  className?: string
  textClassName?: string
  iconClassName?: string
  href?: string
  size?: "sm" | "md" | "lg"
}

export function Logo({ 
  className, 
  textClassName, 
  iconClassName, 
  href = "/",
  size = "md" 
}: LogoProps) {
  const sizeClasses = {
    sm: {
      container: "text-lg",
      icon: "w-6 h-6",
      iconContainer: "w-6 h-6",
      sparkles: "w-4 h-4"
    },
    md: {
      container: "text-xl",
      icon: "w-8 h-8", 
      iconContainer: "w-8 h-8",
      sparkles: "w-5 h-5"
    },
    lg: {
      container: "text-2xl",
      icon: "w-10 h-10",
      iconContainer: "w-10 h-10", 
      sparkles: "w-6 h-6"
    }
  }

  const currentSize = sizeClasses[size]

  return (
    <Link 
      href={href} 
      className={cn(
        "flex items-center gap-2 font-bold transition-opacity hover:opacity-80",
        currentSize.container,
        className
      )}
    >
      <div className={cn(
        "bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",
        currentSize.iconContainer,
        iconClassName
      )}>
        <Sparkles className={cn("text-white", currentSize.sparkles)} />
      </div>
      <span className={cn(
        "bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
        textClassName
      )}>
        WebGenie
      </span>
    </Link>
  )
}
