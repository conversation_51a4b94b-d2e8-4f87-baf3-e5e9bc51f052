import { PredictionServiceClient } from '@google-cloud/aiplatform'
import { google } from '@google-cloud/aiplatform/build/protos/protos'

// Initialize Vertex AI client
const client = new PredictionServiceClient({
  apiEndpoint: `${process.env.VERTEX_AI_LOCATION || 'us-central1'}-aiplatform.googleapis.com`,
})

const project = process.env.GOOGLE_CLOUD_PROJECT
const location = process.env.VERTEX_AI_LOCATION || 'us-central1'
const publisher = 'google'
const model = 'gemini-1.5-pro-001'

export const SYSTEM_PROMPT = `You are a frontend developer. Analyze the user request to understand what type of page they want and return a fully working HTML file styled with Tailwind CSS. It must be responsive, semantic, and well-structured. Do not include explanations.

Page Types to Recognize:
- LOGIN PAGE: Sign-in/sign-up forms with authentication fields
- DASHBOARD: Admin/user dashboard with navigation, stats, charts
- LANDING PAGE: Marketing homepage with hero, features, CTA
- CONTACT PAGE: Contact forms and company information
- ABOUT PAGE: Company/personal information and team details
- PORTFOLIO: Showcase of work/projects with galleries
- BLOG: Article listing or blog post layout
- E-COMMERCE: Product listings, shopping cart, product details
- PRICING: Pricing tables and subscription plans

Requirements:
- Use Tailwind CSS classes for styling
- Make it responsive and mobile-first
- Use semantic HTML elements
- Include proper meta tags and viewport
- Use modern CSS Grid and Flexbox layouts
- Add hover effects and transitions
- Ensure accessibility with proper ARIA labels
- Use a cohesive color scheme
- Include proper typography hierarchy
- Generate content appropriate for the requested page type
- For login pages: include email/password fields, sign-in/sign-up options
- For dashboards: include sidebar navigation, stats cards, data tables
- For landing pages: include hero section, features, testimonials, CTA

Return only the complete HTML code without any explanations or markdown formatting.`

// Page type detection
function detectPageType(prompt: string): string {
  const keywords = prompt.toLowerCase()

  if (keywords.includes('login') || keywords.includes('sign in') || keywords.includes('sign up') || keywords.includes('auth')) {
    return 'login'
  }
  if (keywords.includes('dashboard') || keywords.includes('admin') || keywords.includes('panel')) {
    return 'dashboard'
  }
  if (keywords.includes('contact') || keywords.includes('get in touch') || keywords.includes('contact us')) {
    return 'contact'
  }
  if (keywords.includes('about') || keywords.includes('about us') || keywords.includes('team')) {
    return 'about'
  }
  if (keywords.includes('portfolio') || keywords.includes('work') || keywords.includes('projects')) {
    return 'portfolio'
  }
  if (keywords.includes('blog') || keywords.includes('article') || keywords.includes('news')) {
    return 'blog'
  }
  if (keywords.includes('shop') || keywords.includes('store') || keywords.includes('ecommerce') || keywords.includes('product')) {
    return 'ecommerce'
  }
  if (keywords.includes('pricing') || keywords.includes('plans') || keywords.includes('subscription')) {
    return 'pricing'
  }

  return 'landing'
}

export async function generateWebsite(prompt: string): Promise<string> {
  try {
    if (!project) {
      throw new Error("GOOGLE_CLOUD_PROJECT environment variable is not set")
    }

    const endpoint = `projects/${project}/locations/${location}/publishers/${publisher}/models/${model}`

    const instanceValue = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `${SYSTEM_PROMPT}\n\nUser request: ${prompt}`
            }
          ]
        }
      ],
      generation_config: {
        temperature: 0.7,
        max_output_tokens: 4000,
        top_p: 0.8,
        top_k: 40
      }
    }

    const instances = [instanceValue].map(instance =>
      google.protobuf.Value.fromObject(instance)
    )

    const request = {
      endpoint,
      instances,
    }

    const [response] = await client.predict(request)

    if (!response.predictions || response.predictions.length === 0) {
      throw new Error("No predictions returned from Vertex AI")
    }

    const prediction = response.predictions[0]
    const content = prediction?.structValue?.fields?.candidates?.listValue?.values?.[0]?.structValue?.fields?.content?.structValue?.fields?.parts?.listValue?.values?.[0]?.structValue?.fields?.text?.stringValue

    if (!content) {
      // Fallback to sample HTML if Vertex AI response format is unexpected
      console.warn("Unexpected Vertex AI response format, using fallback")
      return generateSampleHTML(prompt)
    }

    return content
  } catch (error) {
    console.error("Vertex AI API error:", error)
    // Fallback to sample HTML generation for demo purposes
    console.warn("Falling back to sample HTML generation")
    return generateSampleHTML(prompt)
  }
}

// Sample HTML generator for demo purposes
function generateSampleHTML(prompt: string): string {
  const pageType = detectPageType(prompt)

  switch (pageType) {
    case 'login':
      return generateLoginPage(prompt)
    case 'dashboard':
      return generateDashboardPage(prompt)
    case 'contact':
      return generateContactPage(prompt)
    case 'about':
      return generateAboutPage(prompt)
    case 'portfolio':
      return generatePortfolioPage(prompt)
    case 'blog':
      return generateBlogPage(prompt)
    case 'ecommerce':
      return generateEcommercePage(prompt)
    case 'pricing':
      return generatePricingPage(prompt)
    default:
      return generateLandingPage(prompt)
  }
}

// Landing page generator (original logic)
function generateLandingPage(prompt: string): string {
  const title = extractTitle(prompt)
  const theme = extractTheme(prompt)

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">${title}</h1>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-gray-500 hover:text-gray-900">Home</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">About</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Services</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Contact</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">
                    Welcome to ${title}
                </h2>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    ${generateDescription(prompt)}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Get Started
                    </button>
                    <button class="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        Learn More
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Our Features
                </h3>
                <p class="text-xl text-gray-600">
                    Discover what makes us special
                </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                ${generateFeatures(theme)}
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h4 class="text-2xl font-bold mb-4">${title}</h4>
                <p class="text-gray-400 mb-6">
                    Generated with AI-powered WebGenie
                </p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white">Terms</a>
                    <a href="#" class="text-gray-400 hover:text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>`
}

// Login page generator
function generateLoginPage(prompt: string): string {
  const title = prompt.toLowerCase().includes('sign up') ? 'Sign Up' : 'Sign In'

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - WebGenie</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                    <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    ${title} to your account
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    ${title === 'Sign In' ? 'Or' : 'Already have an account?'}
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                        ${title === 'Sign In' ? 'create a new account' : 'sign in here'}
                    </a>
                </p>
            </div>
            <form class="mt-8 space-y-6" action="#" method="POST">
                <div class="rounded-md shadow-sm -space-y-px">
                    ${title === 'Sign Up' ? `
                    <div>
                        <label for="name" class="sr-only">Full name</label>
                        <input id="name" name="name" type="text" required class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" placeholder="Full name">
                    </div>
                    ` : ''}
                    <div>
                        <label for="email-address" class="sr-only">Email address</label>
                        <input id="email-address" name="email" type="email" autocomplete="email" required class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 ${title === 'Sign Up' ? '' : 'rounded-t-md'} focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" placeholder="Email address">
                    </div>
                    <div>
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" name="password" type="password" autocomplete="current-password" required class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" placeholder="Password">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                            Remember me
                        </label>
                    </div>

                    ${title === 'Sign In' ? `
                    <div class="text-sm">
                        <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                            Forgot your password?
                        </a>
                    </div>
                    ` : ''}
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                        </span>
                        ${title}
                    </button>
                </div>

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-2 gap-3">
                        <button type="button" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <svg class="h-5 w-5" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            <span class="ml-2">Google</span>
                        </button>
                        <button type="button" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                            <span class="ml-2">Facebook</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</body>
</html>`
}

// Dashboard page generator
function generateDashboardPage(prompt: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - WebGenie</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="bg-gray-800 text-white w-64 min-h-screen p-4">
            <div class="flex items-center mb-8">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <h1 class="text-xl font-bold">Dashboard</h1>
            </div>

            <nav class="space-y-2">
                <a href="#" class="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    <span>Overview</span>
                </a>
                <a href="#" class="flex items-center space-x-2 text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Analytics</span>
                </a>
                <a href="#" class="flex items-center space-x-2 text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    <span>Users</span>
                </a>
                <a href="#" class="flex items-center space-x-2 text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Reports</span>
                </a>
                <a href="#" class="flex items-center space-x-2 text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Settings</span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-gray-900">Dashboard Overview</h2>
                    <div class="flex items-center space-x-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            New Project
                        </button>
                        <div class="w-8 h-8 bg-gray-300 rounded-full"></div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Users</p>
                                <p class="text-2xl font-bold text-gray-900">2,543</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Revenue</p>
                                <p class="text-2xl font-bold text-gray-900">$45,231</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Orders</p>
                                <p class="text-2xl font-bold text-gray-900">1,423</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Growth</p>
                                <p class="text-2xl font-bold text-gray-900">+12.5%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Tables -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Chart</h3>
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-gray-600">New user registered</span>
                                <span class="text-xs text-gray-400 ml-auto">2 min ago</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span class="text-sm text-gray-600">Order #1234 completed</span>
                                <span class="text-xs text-gray-400 ml-auto">5 min ago</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                <span class="text-sm text-gray-600">Payment pending</span>
                                <span class="text-xs text-gray-400 ml-auto">10 min ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Simple chart initialization
        const ctx = document.getElementById('revenueChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Revenue',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>`
}

function extractTitle(prompt: string): string {
  const keywords = prompt.toLowerCase()
  if (keywords.includes('restaurant')) return 'Delicious Dining'
  if (keywords.includes('portfolio')) return 'Creative Portfolio'
  if (keywords.includes('blog')) return 'My Blog'
  if (keywords.includes('ecommerce') || keywords.includes('shop')) return 'Online Store'
  if (keywords.includes('saas') || keywords.includes('software')) return 'SaaS Platform'
  if (keywords.includes('agency')) return 'Digital Agency'
  if (keywords.includes('fitness') || keywords.includes('gym')) return 'Fitness Studio'
  return 'Modern Website'
}

// Contact page generator
function generateContactPage(prompt: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - WebGenie</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Get in Touch</h1>
                <p class="text-xl text-gray-600">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Send us a message</h2>
                    <form class="space-y-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label for="first-name" class="block text-sm font-medium text-gray-700">First name</label>
                                <input type="text" id="first-name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="last-name" class="block text-sm font-medium text-gray-700">Last name</label>
                                <input type="text" id="last-name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                            <input type="email" id="email" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                            <input type="text" id="subject" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                            <textarea id="message" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            Send Message
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="space-y-8">
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="text-gray-700">123 Business St, City, State 12345</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <span class="text-gray-700">+1 (555) 123-4567</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-gray-700"><EMAIL></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Business Hours</h2>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-700">Monday - Friday</span>
                                <span class="text-gray-900 font-medium">9:00 AM - 6:00 PM</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">Saturday</span>
                                <span class="text-gray-900 font-medium">10:00 AM - 4:00 PM</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">Sunday</span>
                                <span class="text-gray-900 font-medium">Closed</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`
}

// About page generator
function generateAboutPage(prompt: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - WebGenie</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white">
    <div class="min-h-screen">
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">About Our Company</h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                    We're passionate about creating innovative solutions that make a difference in people's lives.
                </p>
            </div>
        </section>

        <!-- Story Section -->
        <section class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div>
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
                        <p class="text-lg text-gray-600 mb-6">
                            Founded in 2020, our company started with a simple mission: to bridge the gap between technology and human needs. What began as a small team of passionate developers has grown into a thriving organization dedicated to innovation.
                        </p>
                        <p class="text-lg text-gray-600">
                            Today, we continue to push boundaries and create solutions that not only meet current needs but anticipate future challenges. Our commitment to excellence drives everything we do.
                        </p>
                    </div>
                    <div class="bg-gray-200 h-96 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 text-lg">Company Image</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <section class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
                    <p class="text-xl text-gray-600">The talented people behind our success</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4"></div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">John Smith</h3>
                        <p class="text-blue-600 font-medium mb-3">CEO & Founder</p>
                        <p class="text-gray-600 text-sm">Visionary leader with 15+ years of experience in technology and business development.</p>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4"></div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Sarah Johnson</h3>
                        <p class="text-blue-600 font-medium mb-3">CTO</p>
                        <p class="text-gray-600 text-sm">Technical expert passionate about creating scalable and innovative solutions.</p>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                        <div class="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4"></div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Mike Davis</h3>
                        <p class="text-blue-600 font-medium mb-3">Head of Design</p>
                        <p class="text-gray-600 text-sm">Creative designer focused on user experience and beautiful, functional interfaces.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Values Section -->
        <section class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
                    <p class="text-xl text-gray-600">The principles that guide everything we do</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Quality</h3>
                        <p class="text-gray-600">We never compromise on quality and always strive for excellence in everything we deliver.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Innovation</h3>
                        <p class="text-gray-600">We embrace new technologies and creative approaches to solve complex challenges.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Collaboration</h3>
                        <p class="text-gray-600">We believe in the power of teamwork and building strong relationships with our clients.</p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>`
}

// Portfolio page generator
function generatePortfolioPage(prompt: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio - WebGenie</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Hero Section -->
        <section class="bg-white py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Our Portfolio</h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Explore our latest projects and see how we've helped businesses achieve their goals through innovative design and development.
                </p>
            </div>
        </section>

        <!-- Filter Tabs -->
        <section class="py-8 bg-white border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-center space-x-8">
                    <button class="px-4 py-2 text-blue-600 border-b-2 border-blue-600 font-medium">All</button>
                    <button class="px-4 py-2 text-gray-500 hover:text-gray-700 font-medium">Web Design</button>
                    <button class="px-4 py-2 text-gray-500 hover:text-gray-700 font-medium">Mobile Apps</button>
                    <button class="px-4 py-2 text-gray-500 hover:text-gray-700 font-medium">Branding</button>
                </div>
            </div>
        </section>

        <!-- Portfolio Grid -->
        <section class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Project 1 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">E-commerce Platform</h3>
                            <p class="text-gray-600 mb-4">Modern online store with advanced features and seamless user experience.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">React</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full">Node.js</span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">MongoDB</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Project →</a>
                        </div>
                    </div>

                    <!-- Project 2 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-green-400 to-blue-500"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Mobile Banking App</h3>
                            <p class="text-gray-600 mb-4">Secure and intuitive mobile banking solution with real-time transactions.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">React Native</span>
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Firebase</span>
                                <span class="px-3 py-1 bg-red-100 text-red-800 text-xs rounded-full">Redux</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Project →</a>
                        </div>
                    </div>

                    <!-- Project 3 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">SaaS Dashboard</h3>
                            <p class="text-gray-600 mb-4">Comprehensive analytics dashboard for business intelligence and reporting.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Vue.js</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full">Express</span>
                                <span class="px-3 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">PostgreSQL</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Project →</a>
                        </div>
                    </div>

                    <!-- Project 4 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-yellow-400 to-orange-500"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Restaurant Website</h3>
                            <p class="text-gray-600 mb-4">Beautiful restaurant website with online ordering and reservation system.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Next.js</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full">Stripe</span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Tailwind</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Project →</a>
                        </div>
                    </div>

                    <!-- Project 5 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-indigo-400 to-purple-500"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Fitness Tracker</h3>
                            <p class="text-gray-600 mb-4">Comprehensive fitness tracking app with social features and challenges.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Flutter</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full">Dart</span>
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">SQLite</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Project →</a>
                        </div>
                    </div>

                    <!-- Project 6 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-pink-400 to-red-500"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Brand Identity</h3>
                            <p class="text-gray-600 mb-4">Complete brand identity design including logo, colors, and marketing materials.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Figma</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full">Illustrator</span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Photoshop</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Project →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-blue-600">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Start Your Project?</h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Let's work together to bring your vision to life. Contact us today to discuss your project requirements.
                </p>
                <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Get Started
                </button>
            </div>
        </section>
    </div>
</body>
</html>`
}

// Blog page generator
function generateBlogPage(prompt: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - WebGenie</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <h1 class="text-3xl font-bold text-gray-900">Our Blog</h1>
                <p class="text-gray-600 mt-2">Insights, tips, and stories from our team</p>
            </div>
        </header>

        <!-- Featured Post -->
        <section class="py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="md:flex">
                        <div class="md:w-1/2">
                            <div class="h-64 md:h-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
                        </div>
                        <div class="md:w-1/2 p-8">
                            <div class="text-sm text-blue-600 font-semibold mb-2">FEATURED POST</div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">The Future of Web Development</h2>
                            <p class="text-gray-600 mb-6">Explore the latest trends and technologies shaping the future of web development, from AI integration to progressive web apps.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>By John Doe</span>
                                <span class="mx-2">•</span>
                                <span>March 15, 2024</span>
                                <span class="mx-2">•</span>
                                <span>5 min read</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Blog Posts Grid -->
        <section class="pb-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Post 1 -->
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-r from-green-400 to-blue-500"></div>
                        <div class="p-6">
                            <div class="text-xs text-blue-600 font-semibold mb-2">TECHNOLOGY</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3">Building Scalable APIs</h3>
                            <p class="text-gray-600 mb-4">Learn best practices for designing and implementing APIs that can handle millions of requests.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>Sarah Johnson</span>
                                <span class="mx-2">•</span>
                                <span>March 10, 2024</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </article>

                    <!-- Post 2 -->
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                        <div class="p-6">
                            <div class="text-xs text-purple-600 font-semibold mb-2">DESIGN</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3">UI/UX Design Trends 2024</h3>
                            <p class="text-gray-600 mb-4">Discover the latest design trends that are shaping user experiences across digital platforms.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>Mike Davis</span>
                                <span class="mx-2">•</span>
                                <span>March 8, 2024</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </article>

                    <!-- Post 3 -->
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-r from-yellow-400 to-orange-500"></div>
                        <div class="p-6">
                            <div class="text-xs text-orange-600 font-semibold mb-2">BUSINESS</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3">Digital Transformation Guide</h3>
                            <p class="text-gray-600 mb-4">A comprehensive guide to successfully implementing digital transformation in your organization.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>Emily Chen</span>
                                <span class="mx-2">•</span>
                                <span>March 5, 2024</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </article>

                    <!-- Post 4 -->
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-r from-indigo-400 to-purple-500"></div>
                        <div class="p-6">
                            <div class="text-xs text-indigo-600 font-semibold mb-2">TUTORIAL</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3">Getting Started with React</h3>
                            <p class="text-gray-600 mb-4">A beginner-friendly tutorial to help you build your first React application from scratch.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>Alex Rodriguez</span>
                                <span class="mx-2">•</span>
                                <span>March 3, 2024</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </article>

                    <!-- Post 5 -->
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-r from-pink-400 to-red-500"></div>
                        <div class="p-6">
                            <div class="text-xs text-red-600 font-semibold mb-2">SECURITY</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3">Web Security Best Practices</h3>
                            <p class="text-gray-600 mb-4">Essential security measures every web developer should implement to protect their applications.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>David Kim</span>
                                <span class="mx-2">•</span>
                                <span>March 1, 2024</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </article>

                    <!-- Post 6 -->
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-r from-teal-400 to-blue-500"></div>
                        <div class="p-6">
                            <div class="text-xs text-teal-600 font-semibold mb-2">PRODUCTIVITY</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3">Developer Tools That Save Time</h3>
                            <p class="text-gray-600 mb-4">Discover essential tools and extensions that can significantly boost your development productivity.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>Lisa Wang</span>
                                <span class="mx-2">•</span>
                                <span>February 28, 2024</span>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </article>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center mt-12">
                    <nav class="flex space-x-2">
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">Previous</button>
                        <button class="px-3 py-2 bg-blue-600 text-white rounded">1</button>
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">2</button>
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">3</button>
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">Next</button>
                    </nav>
                </div>
            </div>
        </section>
    </div>
</body>
</html>`
}

function extractTheme(prompt: string): string {
  const keywords = prompt.toLowerCase()
  if (keywords.includes('restaurant') || keywords.includes('food')) return 'restaurant'
  if (keywords.includes('tech') || keywords.includes('software')) return 'tech'
  if (keywords.includes('creative') || keywords.includes('design')) return 'creative'
  if (keywords.includes('business') || keywords.includes('corporate')) return 'business'
  return 'general'
}

function generateDescription(prompt: string): string {
  const theme = extractTheme(prompt)
  const descriptions = {
    restaurant: 'Experience culinary excellence with our carefully crafted dishes and exceptional service.',
    tech: 'Innovative solutions powered by cutting-edge technology to transform your business.',
    creative: 'Bringing your creative vision to life with stunning design and artistic excellence.',
    business: 'Professional services designed to help your business grow and succeed.',
    general: 'Discover amazing experiences and exceptional quality in everything we do.'
  }
  return descriptions[theme as keyof typeof descriptions] || descriptions.general
}

function generateFeatures(theme: string): string {
  const features = {
    restaurant: [
      { icon: '🍽️', title: 'Fresh Ingredients', desc: 'Locally sourced, organic ingredients in every dish' },
      { icon: '👨‍🍳', title: 'Expert Chefs', desc: 'World-class culinary team with years of experience' },
      { icon: '🏆', title: 'Award Winning', desc: 'Recognized for excellence in fine dining' }
    ],
    tech: [
      { icon: '⚡', title: 'Lightning Fast', desc: 'Optimized performance for the best user experience' },
      { icon: '🔒', title: 'Secure', desc: 'Enterprise-grade security to protect your data' },
      { icon: '📱', title: 'Mobile Ready', desc: 'Responsive design that works on all devices' }
    ],
    creative: [
      { icon: '🎨', title: 'Creative Design', desc: 'Unique and innovative design solutions' },
      { icon: '💡', title: 'Fresh Ideas', desc: 'Original concepts that make you stand out' },
      { icon: '🚀', title: 'Fast Delivery', desc: 'Quick turnaround without compromising quality' }
    ],
    business: [
      { icon: '📈', title: 'Growth Focused', desc: 'Strategies designed to scale your business' },
      { icon: '🤝', title: 'Partnership', desc: 'We work closely with you to achieve your goals' },
      { icon: '💼', title: 'Professional', desc: 'Expert team with proven track record' }
    ],
    general: [
      { icon: '⭐', title: 'Quality', desc: 'Exceptional quality in everything we deliver' },
      { icon: '🎯', title: 'Focused', desc: 'Targeted solutions for your specific needs' },
      { icon: '💪', title: 'Reliable', desc: 'Dependable service you can count on' }
    ]
  }

  const themeFeatures = features[theme as keyof typeof features] || features.general

  return themeFeatures.map(feature => `
    <div class="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
      <div class="text-4xl mb-4">${feature.icon}</div>
      <h4 class="text-xl font-semibold text-gray-900 mb-2">${feature.title}</h4>
      <p class="text-gray-600">${feature.desc}</p>
    </div>
  `).join('')
}
